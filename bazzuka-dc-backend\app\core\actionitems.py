from app.utils.supabase.queries import (
    get_action_items_by_defaulter_id,
    insert_action_item,
    delete_action_item,
    update_action_item,
)

from app.core.ai.payment_likelihood_predictor import predict_payment_likelihood


class ActionItemRepository:
    def __init__(self):
        pass

    def predict_payment_likelihood(self, conversation):
        return predict_payment_likelihood(conversation)

    def get_all_for_issue(self, defaulter_id):
        response = get_action_items_by_defaulter_id(defaulter_id)

        return response.data
    
    def create(
        self,
        action_date: str,
        action_time: str,
        action_channel: str,
        defaulter_id: str,
        action_reason: str,
        payment_likelihood: int = 0,
        category: str = "outreach",
        is_human_followup: bool = False,
    ):

        return insert_action_item(
            action_date,
            action_time,
            action_channel,
            "",
            defaulter_id,
            action_reason,
            payment_likelihood,
            category,
            True,
            is_human_followup,
        )

    # TODO: mark as deleted instead of actually deleting
    def delete(self, id):
        return delete_action_item(id)

    def update(
        self,
        action_item_id: str,
        action_date: str = None,
        action_time: str = None,
        action_channel: str = None,
        defaulter_id: str = None,
        action_reason: str = None,
        payment_likelihood: int = None,
        category: str = None,
    ):
        """
        Update an action item with the given parameters.

        Args:
            action_item_id (str): The ID of the action item to update
            action_date (str, optional): New action date in YYYY-MM-DD format
            action_time (str, optional): New action time in HH:MM:SS format
            action_channel (str, optional): New action channel
            defaulter_id (str, optional): New defaulter ID
            action_reason (str, optional): New action reason
            payment_likelihood (int, optional): New payment likelihood (0-5)
            category (str, optional): New category

        Returns:
            dict: The updated action item data if successful, None if the update failed
        """
        # Create updates dictionary with only non-None values
        updates = {}
        if action_date is not None:
            updates["action_date"] = action_date
        if action_time is not None:
            updates["action_time"] = action_time
        if action_channel is not None:
            updates["action_channel"] = action_channel
        if defaulter_id is not None:
            updates["defaulter_id"] = defaulter_id
        if action_reason is not None:
            updates["action_reason"] = action_reason
        if payment_likelihood is not None:
            updates["payment_likelihood"] = payment_likelihood
        if category is not None:
            updates["category"] = category

        # If no updates were provided, return None
        if not updates:
            return None

        try:
            # Convert action_item_id to int for the database query
            action_item_id = int(action_item_id)
            return update_action_item(action_item_id, updates)
        except (ValueError, TypeError) as e:
            # print(f"Error updating action item: {str(e)}")
            raise e
