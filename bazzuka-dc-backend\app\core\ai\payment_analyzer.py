from app.ai.client import AIClient, Action, PromptGenerator
from app.utils.openai.client import openai as openai_client
from app.core.ai.tools import get_analyzer_tool_engine


class PaymentAnalyzerPromptGenerator(PromptGenerator):
    def __init__(self):
        super().__init__()
        self.prompt = ""
        # with open("data/prompts/payment_analyzer.md", "r") as file:
        #    self.prompt = file.read()
        # WARNING: Payment Analyzer was initialized but we are not using the prompt from the file
        self.prompt = "You are an assistant responsible for analyzing payment activity and enforcing the organization's follow-up strategy. If the user has made a payment, delete the corresponding payment reminder."
        self.inputs_prologue = (
            "Here are the case details and the most recent payment action: "
        )

    def make_inputs(self, args):
        assert args.get("defaulter_id")
        assert args.get("todays_date")

        if args.get("action") == "payment_scheduled":
            assert args.get("payment_plan")
            assert args.get("case_info")
        else:
            assert args.get("amount")
            assert args.get("due_date")

        inputs = f"""
        - **defaulter_id**: {args["defaulter_id"]}
        - **Today's Date**: {args["todays_date"]}
        """
        if args.get("action") == "payment_scheduled":
            inputs += f"""
            - **Case Info**: {args["case_info"]}
            ===
            The following monthly payment plan was scheduled for the user: 
            - **Agreed Plan Details**: {args["payment_plan"]}
            The first payment link has been sent to the user, but the next payment links need to be scheduled along with the payment reminders.
            """
        if args.get("action") == "payment_made":
            inputs += f"""
            The following payment was made by the user:
            - **Amount**: {args["amount"]}
            - **Due Date**: {args["due_date"]}
            """
        if args.get("action") == "payment_missed":
            inputs += f"""
            The user missed the following payment:
            - **Amount**: {args["amount"]}
            - **Due Date**: {args["due_date"]}
            """
        return inputs

    def generate(self, args):
        messages = []
        messages.append(
            {
                "role": "system",
                "content": self.prompt + self.inputs_prologue + self.make_inputs(args),
            }
        )
        return messages


class PaymentAnalyzerAction(Action):
    def __init__(self):
        super().__init__()
        self.prompt_generator = PaymentAnalyzerPromptGenerator()
        self.tool_engine = get_analyzer_tool_engine()


def make_payment_analyzer_client():
    client = AIClient(openai_client)
    payment_analyzer_action = PaymentAnalyzerAction()
    client.register("analyze_payment", payment_analyzer_action)
    return client
